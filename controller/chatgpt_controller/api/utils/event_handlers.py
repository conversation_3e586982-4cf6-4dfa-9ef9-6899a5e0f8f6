"""
Event Handlers for Conversation Monitoring

Event listeners and handlers for processing ChatGPT conversation API responses
and automatically updating the database with conversation content.
"""

import asyncio
import traceback
from typing import Any
from sqlalchemy import select

from ..database import get_db_session
from ..models import Conversation
from .browser_utils import extract_conversation_id_from_url
from .conversation_parser import (
    parse_chatgpt_conversation_data,
    update_conversation_from_api_data
)
from ...core.events import EventType, subscribe_to_event
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


async def update_conversation_in_database(conversation_id: str, conversation_data: Any):
    """Update conversation in database asynchronously"""
    db = None
    try:
        db = await get_db_session()
        success = await update_conversation_from_api_data(
            conversation_id, conversation_data, db
        )
        
        # Check if Conversation exists
        query = select(Conversation).where(Conversation.id == conversation_id)
        result = await db.execute(query)
        
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            logger.warning(f"Conversation {conversation_id} not found in database, created automatically")
            conversation = Conversation(id=conversation_id)
            db.add(conversation)
            await db.commit()

        if success:
            logger.success(f"✅ Successfully updated conversation {conversation_id} in database")
        else:
            logger.warning(f"⚠️ Failed to update conversation {conversation_id} in database")

    except Exception as e:
        logger.error(f"❌ Error updating conversation {conversation_id} in database: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
    finally:
        if db:
            await db.close()


def handle_conversation_api_response(event):
    """Handle ChatGPT conversation API response events"""
    try:
        data = event.data
        message_type = data.get("message_type")

        # Only handle conversation API responses
        if message_type != "monitored_api_response_data":
            return

        api_type = data.get("api_type")
        if api_type != "conversation":
            logger.debug(f"||| Skipping non-conversation API type: {api_type}")
            return

        # Get URL from the nested data structure
        nested_data = data.get("data", {})
        request_data = nested_data.get("request", {})
        url = request_data.get("url", "")
        response_data = nested_data.get("response")

        if not response_data or not url:
            logger.debug("🔍 Skipping - missing response data or URL")
            return

        # Skip textdocs endpoints
        if "/textdocs" in url:
            logger.debug(f"🔍 Skipping textdocs endpoint: {url}")
            return

        # Skip ChatGPT backend API URLs with query parameters (like url_safe)
        if "backend-api/conversation/" in url and ("?" in url or "&" in url):
            logger.debug(f"🔍 Skipping backend API URL with query parameters: {url}")
            return

        # Extract conversation ID from URL
        conversation_id = extract_conversation_id_from_url(url)
        if not conversation_id:
            logger.debug(f"🔍 No conversation ID found in URL: {url}")
            return

        logger.info(f"🎯 Processing conversation API response for {conversation_id}")

        # Extract the actual conversation content from the response
        # The conversation data is in response_data.content, not response_data directly
        conversation_content = response_data.get("content")
        if not conversation_content:
            logger.debug("🔍 No conversation content found in response data")
            return

        # Parse conversation data
        conversation_data = parse_chatgpt_conversation_data(conversation_content)
        if not conversation_data:
            logger.warning(f"⚠️ Failed to parse conversation data for {conversation_id}")
            logger.debug(f"🔍 Raw conversation content: {conversation_content}")
            return

        logger.success(f"✅ Successfully parsed conversation data for {conversation_id}")

        # Update conversation in database asynchronously
        try:
            # Try to create a task in the current event loop
            asyncio.create_task(update_conversation_in_database(conversation_id, conversation_data))
        except RuntimeError:
            # If no event loop is running, run in a new thread
            import threading
            def run_db_update():
                asyncio.run(update_conversation_in_database(conversation_id, conversation_data))

            thread = threading.Thread(target=run_db_update, daemon=True)
            thread.start()

    except Exception as e:
        logger.error(f"❌ Error handling conversation API response: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")


def setup_conversation_event_listeners():
    """Setup event listeners for conversation monitoring"""
    try:
        subscribe_to_event(EventType.MESSAGE_RECEIVED, handle_conversation_api_response)
        logger.info("📡 Conversation event listeners registered")
    except Exception as e:
        logger.error(f"❌ Error setting up conversation event listeners: {e}")
