"""
Research Result Formatter

Utility functions for formatting research results with content references
into standard markdown format.
"""

import re
from typing import List, Dict, Any, Optional, Union
from urllib.parse import urlparse
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


class ContentSegment:
    """Represents a segment of content (either text or citation)"""
    
    def __init__(self, content: str, segment_type: str = "text", citation_data: Optional[Dict[str, Any]] = None):
        self.content = content
        self.segment_type = segment_type  # "text" or "citation"
        self.citation_data = citation_data or {}


def extract_base_url(url: str) -> str:
    """Extract base URL from a full URL"""
    try:
        parsed = urlparse(url)
        return f"{parsed.netloc}"
    except Exception:
        return url


def parse_content_with_references(content: str, content_references: List[Dict[str, Any]]) -> List[ContentSegment]:
    """
    Parse message content and split it into segments based on content references.
    
    Args:
        content: The original message content
        content_references: List of content reference objects
        
    Returns:
        List of ContentSegment objects representing text and citations
    """
    if not content_references:
        return [ContentSegment(content, "text")]
    
    # Sort references by start_idx to process them in order
    sorted_refs = sorted(content_references, key=lambda x: x.get("start_idx", 0))
    
    segments = []
    current_pos = 0
    
    for ref in sorted_refs:
        start_idx = ref.get("start_idx", 0)
        end_idx = ref.get("end_idx", 0)
        
        # Validate indices
        if start_idx < 0 or end_idx <= start_idx or end_idx > len(content):
            logger.warning(f"Invalid reference indices: start={start_idx}, end={end_idx}, content_length={len(content)}")
            continue
            
        # Add text segment before citation (if any)
        if current_pos < start_idx:
            text_content = content[current_pos:start_idx]
            if text_content:
                segments.append(ContentSegment(text_content, "text"))
        
        # Add citation segment
        citation_text = content[start_idx:end_idx]
        segments.append(ContentSegment(citation_text, "citation", ref))
        
        current_pos = end_idx
    
    # Add remaining text after last citation (if any)
    if current_pos < len(content):
        remaining_text = content[current_pos:]
        if remaining_text:
            segments.append(ContentSegment(remaining_text, "text"))
    
    return segments


def format_citation_markdown(citation_data: Dict[str, Any]) -> str:
    """
    Format a citation into markdown link format.
    
    Args:
        citation_data: Citation data containing url, title, attribution, etc.
        
    Returns:
        Formatted markdown link string
    """
    url = citation_data.get("url", "")
    title = citation_data.get("title", "")
    attribution = citation_data.get("attribution", "")
    
    if not url:
        # If no URL, return the original matched text or a placeholder
        return citation_data.get("matched_text", "[Citation]")
    
    # Extract base URL for display
    base_url = extract_base_url(url)
    
    # Use attribution if available, otherwise use base_url
    display_text = attribution if attribution else base_url
    
    # Format as markdown link
    return f" ([{display_text}]({url})) "


def format_research_result(content: str, metadata: Dict[str, Any]) -> Optional[str]:
    """
    Format a research result message by replacing citation markers with markdown links.
    
    Args:
        content: The original message content
        metadata: Message metadata containing content_references
        
    Returns:
        Formatted markdown content with proper citations, or None if not a research result
    """
    # Check if this message has content_references (indicating it's a research result)
    content_references = metadata.get("content_references", [])
    if not content_references:
        return None
    
    try:
        # Parse content into segments
        segments = parse_content_with_references(content, content_references)
        
        # Format each segment
        formatted_parts = []
        for segment in segments:
            if segment.segment_type == "text":
                formatted_parts.append(segment.content)
            elif segment.segment_type == "citation":
                formatted_citation = format_citation_markdown(segment.citation_data)
                formatted_parts.append(formatted_citation)
        
        # Join all parts
        formatted_content = "".join(formatted_parts)
        
        logger.debug(f"Successfully formatted research result: {len(content_references)} citations processed")
        return formatted_content
        
    except Exception as e:
        logger.error(f"Error formatting research result: {e}")
        return None


def find_last_research_result(messages: List[Dict[str, Any]]) -> Optional[str]:
    """
    Find the last research result in a list of messages and format it.
    
    Args:
        messages: List of message dictionaries sorted by time (newest first)
        
    Returns:
        Formatted research result content, or None if no research result found
    """
    # Iterate through messages in reverse chronological order
    for message in messages:
        metadata = message.get("metadata", {})
        content = message.get("content", "")
        
        # Check if this message has content_references
        if metadata.get("content_references"):
            formatted_result = format_research_result(content, metadata)
            if formatted_result:
                logger.info("Found and formatted last research result")
                return formatted_result
    
    logger.debug("No research result found in messages")
    return None
