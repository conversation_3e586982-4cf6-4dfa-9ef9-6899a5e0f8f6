#!/usr/bin/env python3
"""
Simple test script for research result formatter functionality.
Does not require pytest - uses basic assertions.
"""

import sys
import os
from datetime import datetime

# Add the controller directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'controller'))

from chatgpt_controller.api.utils.research_formatter import (
    extract_base_url,
    parse_content_with_references,
    format_citation_markdown,
    format_research_result,
    find_last_research_result
)


def test_extract_base_url():
    """Test base URL extraction"""
    print("Testing extract_base_url...")
    
    # Test normal URL
    url = "https://wallstreetcn.com/articles/3751469#:~:text=2025"
    result = extract_base_url(url)
    assert result == "wallstreetcn.com", f"Expected 'wallstreetcn.com', got '{result}'"
    
    # Test URL with subdomain
    url = "https://www.example.com/path/to/page"
    result = extract_base_url(url)
    assert result == "www.example.com", f"Expected 'www.example.com', got '{result}'"
    
    print("✅ extract_base_url tests passed")


def test_parse_content_with_references():
    """Test content parsing with references"""
    print("Testing parse_content_with_references...")
    
    # Test with no references
    content = "This is a simple text without any citations."
    references = []
    segments = parse_content_with_references(content, references)
    assert len(segments) == 1
    assert segments[0].segment_type == "text"
    assert segments[0].content == content
    
    # Test with single reference
    content = "This is text with 【2†L31-L34】 a citation in the middle."
    references = [
        {
            "matched_text": "【2†L31-L34】",
            "start_idx": 18,
            "end_idx": 29,
            "url": "https://example.com",
            "title": "Example Title",
            "attribution": "example.com"
        }
    ]
    segments = parse_content_with_references(content, references)
    assert len(segments) == 3
    assert segments[0].segment_type == "text"
    assert segments[0].content == "This is text with "
    assert segments[1].segment_type == "citation"
    assert segments[1].content == "【2†L31-L34】"
    assert segments[2].segment_type == "text"
    assert segments[2].content == " a citation in the middle."
    
    print("✅ parse_content_with_references tests passed")


def test_format_citation_markdown():
    """Test citation formatting"""
    print("Testing format_citation_markdown...")
    
    # Test with attribution
    citation_data = {
        "matched_text": "【2†L31-L34】",
        "url": "https://wallstreetcn.com/articles/3751469",
        "title": "三大加密货币法案通过！",
        "attribution": "wallstreetcn.com"
    }
    result = format_citation_markdown(citation_data)
    expected = " ([wallstreetcn.com](https://wallstreetcn.com/articles/3751469)) "
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test without attribution
    citation_data = {
        "matched_text": "【2†L31-L34】",
        "url": "https://example.com/path",
        "title": "Example Title"
    }
    result = format_citation_markdown(citation_data)
    expected = " ([example.com](https://example.com/path)) "
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test without URL
    citation_data = {
        "matched_text": "【2†L31-L34】",
        "title": "Example Title"
    }
    result = format_citation_markdown(citation_data)
    assert result == "【2†L31-L34】", f"Expected '【2†L31-L34】', got '{result}'"
    
    print("✅ format_citation_markdown tests passed")


def test_format_research_result():
    """Test research result formatting"""
    print("Testing format_research_result...")
    
    # Test with citations
    content = "2025年7月17日，当地时间美国众议院悉数通过了【2†L31-L34】三项加密货币相关法案。"
    # Find the correct indices for the citation
    citation_text = "【2†L31-L34】"
    start_idx = content.find(citation_text)
    end_idx = start_idx + len(citation_text)

    metadata = {
        "content_references": [
            {
                "matched_text": citation_text,
                "start_idx": start_idx,
                "end_idx": end_idx,
                "url": "https://wallstreetcn.com/articles/3751469",
                "title": "三大加密货币法案通过！",
                "attribution": "wallstreetcn.com"
            }
        ]
    }
    result = format_research_result(content, metadata)
    expected = "2025年7月17日，当地时间美国众议院悉数通过了 ([wallstreetcn.com](https://wallstreetcn.com/articles/3751469)) 三项加密货币相关法案。"
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test without references
    content = "This is regular content without citations."
    metadata = {}
    result = format_research_result(content, metadata)
    assert result is None, f"Expected None, got '{result}'"
    
    print("✅ format_research_result tests passed")


def test_find_last_research_result():
    """Test finding last research result"""
    print("Testing find_last_research_result...")
    
    # Test with research result found
    content_with_citation = "Research result with 【1†】 citation"
    citation_text = "【1†】"
    start_idx = content_with_citation.find(citation_text)
    end_idx = start_idx + len(citation_text)

    messages = [
        {
            "content": "Regular message without citations",
            "metadata": {},
            "created_at": datetime(2024, 1, 1, 10, 0),
            "role": "assistant"
        },
        {
            "content": content_with_citation,
            "metadata": {
                "content_references": [
                    {
                        "matched_text": citation_text,
                        "start_idx": start_idx,
                        "end_idx": end_idx,
                        "url": "https://example.com",
                        "attribution": "example.com"
                    }
                ]
            },
            "created_at": datetime(2024, 1, 1, 12, 0),
            "role": "assistant"
        },
        {
            "content": "Another regular message",
            "metadata": {},
            "created_at": datetime(2024, 1, 1, 14, 0),
            "role": "user"
        }
    ]
    result = find_last_research_result(messages)
    expected = "Research result with ([example.com](https://example.com)) citation"
    assert result == expected, f"Expected '{expected}', got '{result}'"
    
    # Test with no research result
    messages = [
        {
            "content": "Regular message 1",
            "metadata": {},
            "created_at": datetime(2024, 1, 1, 10, 0),
            "role": "assistant"
        },
        {
            "content": "Regular message 2",
            "metadata": {},
            "created_at": datetime(2024, 1, 1, 12, 0),
            "role": "user"
        }
    ]
    result = find_last_research_result(messages)
    assert result is None, f"Expected None, got '{result}'"
    
    print("✅ find_last_research_result tests passed")


def test_complex_example():
    """Test with the complex example from the user's request"""
    print("Testing complex example...")

    content = "2025年7月17日，当地时间美国众议院悉数通过了【2†L31-L34】三项加密货币相关法案。"

    # Find the correct indices for the citation
    citation_text = "【2†L31-L34】"
    start_idx = content.find(citation_text)
    end_idx = start_idx + len(citation_text)

    metadata = {
        "content_references": [
            {
                "matched_text": citation_text,
                "start_idx": start_idx,
                "end_idx": end_idx,
                "alt": None,
                "type": "webpage_extended",
                "title": "三大加密货币法案通过！",
                "url": "https://wallstreetcn.com/articles/3751469#:~:text=2025",
                "pub_date": None,
                "snippet": "Sample snippet",
                "attribution": "wallstreetcn.com",
                "icon_type": None
            }
        ]
    }

    result = format_research_result(content, metadata)

    # Check that the citation was replaced correctly
    assert "([wallstreetcn.com](https://wallstreetcn.com/articles/3751469#:~:text=2025))" in result
    assert "【2†L31-L34】" not in result

    print("✅ Complex example test passed")
    print(f"Result: {result}")


def main():
    """Run all tests"""
    print("🧪 Running research formatter tests...\n")
    
    try:
        test_extract_base_url()
        test_parse_content_with_references()
        test_format_citation_markdown()
        test_format_research_result()
        test_find_last_research_result()
        test_complex_example()
        
        print("\n🎉 All tests passed successfully!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
