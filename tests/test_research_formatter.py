"""
Unit tests for research result formatter functionality.

Tests the formatting of research results with content references into
standard markdown format.
"""

import pytest
from datetime import datetime
from controller.chatgpt_controller.api.utils.research_formatter import (
    ContentSegment,
    extract_base_url,
    parse_content_with_references,
    format_citation_markdown,
    format_research_result,
    find_last_research_result
)


class TestExtractBaseUrl:
    """Test base URL extraction functionality"""
    
    def test_extract_base_url_normal(self):
        """Test extracting base URL from normal URLs"""
        url = "https://wallstreetcn.com/articles/3751469#:~:text=2025"
        result = extract_base_url(url)
        assert result == "wallstreetcn.com"
    
    def test_extract_base_url_with_subdomain(self):
        """Test extracting base URL with subdomain"""
        url = "https://www.example.com/path/to/page"
        result = extract_base_url(url)
        assert result == "www.example.com"
    
    def test_extract_base_url_invalid(self):
        """Test extracting base URL from invalid URL"""
        url = "not-a-url"
        result = extract_base_url(url)
        assert result == "not-a-url"


class TestParseContentWithReferences:
    """Test content parsing with references"""
    
    def test_parse_content_no_references(self):
        """Test parsing content without references"""
        content = "This is a simple text without any citations."
        references = []
        
        segments = parse_content_with_references(content, references)
        
        assert len(segments) == 1
        assert segments[0].segment_type == "text"
        assert segments[0].content == content
    
    def test_parse_content_single_reference(self):
        """Test parsing content with single reference"""
        content = "This is text with 【2†L31-L34】 a citation in the middle."
        references = [
            {
                "matched_text": "【2†L31-L34】",
                "start_idx": 18,
                "end_idx": 29,
                "url": "https://example.com",
                "title": "Example Title",
                "attribution": "example.com"
            }
        ]
        
        segments = parse_content_with_references(content, references)
        
        assert len(segments) == 3
        assert segments[0].segment_type == "text"
        assert segments[0].content == "This is text with "
        assert segments[1].segment_type == "citation"
        assert segments[1].content == "【2†L31-L34】"
        assert segments[2].segment_type == "text"
        assert segments[2].content == " a citation in the middle."
    
    def test_parse_content_multiple_references(self):
        """Test parsing content with multiple references"""
        content = "Text 【1†】 more text 【2†】 end."
        references = [
            {
                "matched_text": "【1†】",
                "start_idx": 5,
                "end_idx": 9,
                "url": "https://example1.com",
                "attribution": "example1.com"
            },
            {
                "matched_text": "【2†】",
                "start_idx": 20,
                "end_idx": 24,
                "url": "https://example2.com",
                "attribution": "example2.com"
            }
        ]
        
        segments = parse_content_with_references(content, references)
        
        assert len(segments) == 5
        assert segments[0].content == "Text "
        assert segments[1].content == "【1†】"
        assert segments[2].content == " more text "
        assert segments[3].content == "【2†】"
        assert segments[4].content == " end."
    
    def test_parse_content_invalid_indices(self):
        """Test parsing content with invalid reference indices"""
        content = "Short text"
        references = [
            {
                "matched_text": "【invalid】",
                "start_idx": 50,  # Beyond content length
                "end_idx": 60,
                "url": "https://example.com"
            }
        ]
        
        segments = parse_content_with_references(content, references)
        
        # Should return original content as single text segment
        assert len(segments) == 1
        assert segments[0].segment_type == "text"
        assert segments[0].content == content


class TestFormatCitationMarkdown:
    """Test citation formatting functionality"""
    
    def test_format_citation_with_attribution(self):
        """Test formatting citation with attribution"""
        citation_data = {
            "matched_text": "【2†L31-L34】",
            "url": "https://wallstreetcn.com/articles/3751469",
            "title": "三大加密货币法案通过！",
            "attribution": "wallstreetcn.com"
        }
        
        result = format_citation_markdown(citation_data)
        expected = "([wallstreetcn.com](https://wallstreetcn.com/articles/3751469))"
        assert result == expected
    
    def test_format_citation_without_attribution(self):
        """Test formatting citation without attribution"""
        citation_data = {
            "matched_text": "【2†L31-L34】",
            "url": "https://example.com/path",
            "title": "Example Title"
        }
        
        result = format_citation_markdown(citation_data)
        expected = "([example.com](https://example.com/path))"
        assert result == expected
    
    def test_format_citation_no_url(self):
        """Test formatting citation without URL"""
        citation_data = {
            "matched_text": "【2†L31-L34】",
            "title": "Example Title"
        }
        
        result = format_citation_markdown(citation_data)
        assert result == "【2†L31-L34】"


class TestFormatResearchResult:
    """Test research result formatting"""
    
    def test_format_research_result_with_citations(self):
        """Test formatting research result with citations"""
        content = "2025年7月17日，当地时间美国众议院悉数通过了【2†L31-L34】三项加密货币相关法案。"
        metadata = {
            "content_references": [
                {
                    "matched_text": "【2†L31-L34】",
                    "start_idx": 21,
                    "end_idx": 32,
                    "url": "https://wallstreetcn.com/articles/3751469",
                    "title": "三大加密货币法案通过！",
                    "attribution": "wallstreetcn.com"
                }
            ]
        }
        
        result = format_research_result(content, metadata)
        expected = "2025年7月17日，当地时间美国众议院悉数通过了([wallstreetcn.com](https://wallstreetcn.com/articles/3751469))三项加密货币相关法案。"
        assert result == expected
    
    def test_format_research_result_no_references(self):
        """Test formatting content without references"""
        content = "This is regular content without citations."
        metadata = {}
        
        result = format_research_result(content, metadata)
        assert result is None
    
    def test_format_research_result_empty_references(self):
        """Test formatting content with empty references list"""
        content = "This is regular content."
        metadata = {"content_references": []}
        
        result = format_research_result(content, metadata)
        assert result is None


class TestFindLastResearchResult:
    """Test finding last research result in messages"""
    
    def test_find_last_research_result_found(self):
        """Test finding research result in message list"""
        messages = [
            {
                "content": "Regular message without citations",
                "metadata": {},
                "created_at": datetime(2024, 1, 1, 10, 0),
                "role": "assistant"
            },
            {
                "content": "Research result with 【1†】 citation",
                "metadata": {
                    "content_references": [
                        {
                            "matched_text": "【1†】",
                            "start_idx": 20,
                            "end_idx": 24,
                            "url": "https://example.com",
                            "attribution": "example.com"
                        }
                    ]
                },
                "created_at": datetime(2024, 1, 1, 12, 0),
                "role": "assistant"
            },
            {
                "content": "Another regular message",
                "metadata": {},
                "created_at": datetime(2024, 1, 1, 14, 0),
                "role": "user"
            }
        ]
        
        result = find_last_research_result(messages)
        expected = "Research result with ([example.com](https://example.com)) citation"
        assert result == expected
    
    def test_find_last_research_result_not_found(self):
        """Test when no research result is found"""
        messages = [
            {
                "content": "Regular message 1",
                "metadata": {},
                "created_at": datetime(2024, 1, 1, 10, 0),
                "role": "assistant"
            },
            {
                "content": "Regular message 2",
                "metadata": {},
                "created_at": datetime(2024, 1, 1, 12, 0),
                "role": "user"
            }
        ]
        
        result = find_last_research_result(messages)
        assert result is None
    
    def test_find_last_research_result_empty_list(self):
        """Test with empty message list"""
        messages = []
        
        result = find_last_research_result(messages)
        assert result is None


if __name__ == "__main__":
    pytest.main([__file__])
